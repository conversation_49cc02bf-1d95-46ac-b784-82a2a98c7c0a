/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) MAINTASK_OP_T  mainTaskOp;

/*******************************************************************************
* Function Name  : taskMainWinInit
* Description    : taskMainWinInit function.
* Input          : main_id: 0: use default, 0xfffffff: black
* Output         : none
* Return         : none
*******************************************************************************/
void taskMainWinInit(u32 main_id, u32 sub_type, u32 sub_id, u32 sub_size,u32 win_type)
{
	mainTaskOp.main_id  = main_id;
	mainTaskOp.sub_type = sub_type;
	mainTaskOp.sub_id   = sub_id;
	mainTaskOp.sub_size = sub_size;
	mainTaskOp.wintype  = win_type;
}
/*******************************************************************************
* Function Name  : taskMainWinChangeInit
* Description    : taskMainWinChangeInit function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskMainWinChangeInit(void)
{
	hx330x_bytes_memset((u8*)&mainTaskOp, 0, sizeof(mainTaskOp));
#if 1 //按最大video size显示
	hal_lcdGetVideoPos(&mainTaskOp.x, &mainTaskOp.y);
	hal_lcdGetVideoResolution(&mainTaskOp.ratio_w,&mainTaskOp.ratio_h);
	hal_lcdGetVideoResolution(&mainTaskOp.dest_w,&mainTaskOp.dest_h);
#else  //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&mainTaskOp.x, &mainTaskOp.y);
	hal_lcdGetVideoRatioResolution(&mainTaskOp.ratio_w,&mainTaskOp.ratio_h);
	hal_lcdGetVideoRatioDestResolution(&mainTaskOp.dest_w,&mainTaskOp.dest_h);
#endif

	mainTaskOp.main_stride = (mainTaskOp.ratio_w + 0x1f)&~0x1f;
	mainTaskOp.wintype = MAIN_WIN_NONE;

	mainTaskOp.winChangeInterval = 10; //ms
	//deg_Printf("taskMainWinChangeInit:[%d,%d][%d,%d][%d,%d]\n",mainTaskOp.x, mainTaskOp.y,
	//mainTaskOp.ratio_w,mainTaskOp.ratio_h, mainTaskOp.dest_w, mainTaskOp.dest_h);
}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskMainWinChangeUint(void)
{
	if(mainTaskOp.wintype == MAIN_WIN_NONE || mainTaskOp.wintype >= MAIN_WIN_MAX )
	{
		return;
	}
	if(mainTaskOp.main_yaddr)
		hal_sysMemFree(mainTaskOp.main_yaddr);
	if(mainTaskOp.sub_yaddr)
		hal_sysMemFree(mainTaskOp.sub_yaddr);
	if(mainTaskOp.sub_type == MEDIA_SRC_RAM)
	{
		if(mainTaskOp.sub_id && mainTaskOp.sub_size)
			hal_sysMemFree((void*)mainTaskOp.sub_id);

	}
	mainTaskOp.main_id    = 0;
	mainTaskOp.sub_id     = 0;
	mainTaskOp.main_yaddr = NULL;
	mainTaskOp.sub_yaddr  = NULL;
	mainTaskOp.wintype = MAIN_WIN_NONE;
}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int taskMainWinChangeKick(void)
{
	int res = 0;
	u32 min_step;
	if(mainTaskOp.wintype == MAIN_WIN_NONE || mainTaskOp.wintype >= MAIN_WIN_MAX )
	{
		return -1;
	}
	mainTaskOp.main_yaddr = hal_sysMemMalloc(mainTaskOp.main_stride * mainTaskOp.ratio_h *3/2);
	mainTaskOp.sub_yaddr = hal_sysMemMalloc(mainTaskOp.main_stride * mainTaskOp.ratio_h *3/2);
	if(mainTaskOp.main_yaddr == NULL || mainTaskOp.sub_yaddr == NULL)
	{
		res = -1;
		goto OUT;
	}
	mainTaskOp.main_uvaddr = mainTaskOp.main_yaddr + mainTaskOp.main_stride * mainTaskOp.ratio_h;
	mainTaskOp.sub_uvaddr  = mainTaskOp.sub_yaddr + mainTaskOp.main_stride * mainTaskOp.ratio_h;
	min_step = hx330x_greatest_divisor(mainTaskOp.ratio_w, mainTaskOp.ratio_h);
#if 1//LCDSHOW_UI_SMALL
	mainTaskOp.w_step = mainTaskOp.ratio_w/min_step * 4;
	mainTaskOp.h_step = mainTaskOp.ratio_h/min_step * 4;
	if((mainTaskOp.ratio_w / mainTaskOp.w_step) < 10)
	{
		mainTaskOp.w_step = mainTaskOp.ratio_w/(min_step * 4);
		mainTaskOp.h_step = mainTaskOp.ratio_h/(min_step * 4);
	}

#else
	mainTaskOp.w_step = mainTaskOp.ratio_w/min_step * 4;
	mainTaskOp.h_step = mainTaskOp.ratio_h/min_step * 4;
#endif
	//mainTaskOp.w_step = mainTaskOp.ratio_w/min_step * 4;
	//mainTaskOp.h_step = mainTaskOp.ratio_h/min_step * 4;

	deg_Printf("kick min_step[%d]%d,%d\n",min_step,mainTaskOp.w_step,mainTaskOp.h_step);
	deg_Printf("mainTaskOp.main_id: %x,mainTaskOp.sub_id :%x\n",mainTaskOp.main_id,mainTaskOp.sub_id );
	if(mainTaskOp.main_id == 0)
	{
		//res = res_image_decode(mainMenuTab[mainTaskOp.curId].resId, mainTaskOp.main_yaddr, mainTaskOp.ratio_w, mainTaskOp.ratio_h);
	}else if(mainTaskOp.main_id != INVALID_RES_ID)
	{
		res = res_image_decode(mainTaskOp.main_id, mainTaskOp.main_yaddr, mainTaskOp.ratio_w, mainTaskOp.ratio_h);
	}else
	{
		hx330x_bytes_memset(mainTaskOp.main_yaddr,0x00,mainTaskOp.main_stride*mainTaskOp.ratio_h);
        hx330x_bytes_memset(mainTaskOp.main_uvaddr,0x80,mainTaskOp.main_stride*mainTaskOp.ratio_h/2);
		hx330x_sysDcacheWback((u32)mainTaskOp.main_yaddr,mainTaskOp.main_stride*mainTaskOp.ratio_h*3/2);
	}
	hx330x_sysDcacheInvalid((u32)mainTaskOp.main_yaddr,mainTaskOp.main_stride*mainTaskOp.ratio_h*3/2);

	if(res < 0)
	{
		res = -2;
		goto OUT;
	}
	if(mainTaskOp.sub_id == INVALID_RES_ID)
	{
		hx330x_bytes_memset(mainTaskOp.sub_yaddr,0x00,mainTaskOp.main_stride*mainTaskOp.ratio_h);
        hx330x_bytes_memset(mainTaskOp.sub_uvaddr,0x80,mainTaskOp.main_stride*mainTaskOp.ratio_h/2);
		hx330x_sysDcacheWback((u32)mainTaskOp.sub_yaddr,mainTaskOp.main_stride*mainTaskOp.ratio_h*3/2);
	}else
	{
		//res = taskComDecodeImg(mainTaskOp.sub_type, mainTaskOp.sub_id, mainTaskOp.sub_size, mainTaskOp.sub_yaddr,mainTaskOp.ratio_w, mainTaskOp.ratio_h);
	}
	hx330x_sysDcacheInvalid((u32)(u32)mainTaskOp.sub_yaddr,mainTaskOp.main_stride*mainTaskOp.ratio_h*3/2);
	if(res < 0)
	{
		res = -3;
		goto OUT;
	}
OUT:
	if(res < 0)
	{
		deg_Printf("taskMainWinChangeKick fail:%d\n", res);
		taskMainWinChangeUint();
	}
	return res;
}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskWinCenterProcess(void)
{
	lcdshow_frame_t *p_lcd_buffer = NULL;
	s16 sx, ex, sy, ey;
	u32 i;
	//deg_Printf("taskWinCenterProcess:%d\n",mainTaskOp.wintype);
	if(mainTaskOp.wintype != SUB_TO_MAIN_CENTER && mainTaskOp.wintype != MAIN_TO_SUB_CENTER)
	{
		return;
	}
	mainTaskOp.w_step /= 2;
	mainTaskOp.h_step /= 2;
	if(mainTaskOp.wintype == SUB_TO_MAIN_CENTER)
	{
		sx = mainTaskOp.w_step;
		ex = mainTaskOp.ratio_w -  mainTaskOp.w_step;
		sy = mainTaskOp.h_step;
		ey = mainTaskOp.ratio_h - mainTaskOp.h_step;
	}else
	{
		sx = mainTaskOp.ratio_w/2 - mainTaskOp.w_step;
		ex = mainTaskOp.ratio_w/2 + mainTaskOp.w_step;
		sy = mainTaskOp.ratio_h/2 - mainTaskOp.h_step;
		ey = mainTaskOp.ratio_h/2 + mainTaskOp.h_step;
	}
	//deg_Printf("s [%d,%d,%d,%d]\n", sx, ex, sy, ey);
	u32 time = XOSTimeGet();
	while(1)
	{
		do {
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
			hal_wdtClear();
		}while(p_lcd_buffer == NULL);
		hal_lcdVideoFrameFlush(p_lcd_buffer, mainTaskOp.x, mainTaskOp.y, mainTaskOp.ratio_w, mainTaskOp.ratio_h, mainTaskOp.dest_w, mainTaskOp.dest_h);
		hx330x_sysDcacheInvalid((u32)p_lcd_buffer->y_addr,p_lcd_buffer->buf_size);
		for(i = 0; i < mainTaskOp.ratio_h; i++)
		{
			hal_wdtClear();
			if(i < sy || i >= ey)
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i, (void*)mainTaskOp.main_yaddr + mainTaskOp.main_stride * i, mainTaskOp.ratio_w);
				if((i&1) == 0)
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2, (void*)mainTaskOp.main_uvaddr + mainTaskOp.main_stride * i/2, mainTaskOp.ratio_w);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i, (void*)mainTaskOp.main_yaddr + mainTaskOp.main_stride * i, sx);
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i + sx, (void*)mainTaskOp.sub_yaddr + mainTaskOp.main_stride * i + sx,  ex - sx);
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i + ex, (void*)mainTaskOp.main_yaddr + mainTaskOp.main_stride * i + ex, mainTaskOp.ratio_w - ex);
				if((i&1) == 0)
				{
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2, (void*)mainTaskOp.main_uvaddr + mainTaskOp.main_stride * i/2, sx);
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2 + sx, (void*)mainTaskOp.sub_uvaddr + mainTaskOp.main_stride * i/2 + sx,  ex - sx);
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2 + ex, (void*)mainTaskOp.main_uvaddr + mainTaskOp.main_stride * i/2 + ex, mainTaskOp.ratio_w - ex);
				}

			}
		}
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		p_lcd_buffer = NULL;
		if(mainTaskOp.wintype == SUB_TO_MAIN_CENTER)
		{
			if(ex <= sx || ey <= sy)
			{
				break;
			}
			sx += mainTaskOp.w_step;
			ex -= mainTaskOp.w_step;
			sy += mainTaskOp.h_step;
			ey -= mainTaskOp.h_step;

		}else
		{
			if(((ex - sx) >= mainTaskOp.ratio_w) || ((ey - sy) >= mainTaskOp.ratio_h))
			{
				break;
			}
			sx -= mainTaskOp.w_step;
			ex += mainTaskOp.w_step;
			sy -= mainTaskOp.h_step;
			ey += mainTaskOp.h_step;
		}
		//deg_Printf("[%d,%d,%d,%d]\n", sx, ex, sy, ey);
		//deg_Printf("time:%dms\n", XOSTimeGet() - time );
		if((mainTaskOp.winChangeInterval + time) > XOSTimeGet() )
		{
			XOSTimeDly((mainTaskOp.winChangeInterval + time) - XOSTimeGet());
		}
		time = XOSTimeGet();
	}
	taskMainWinChangeUint();

}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskWinHorProcess(void)
{
	lcdshow_frame_t *p_lcd_buffer = NULL;
	s16 sx, ex;
	u8* main_y;
	u8* main_uv;
	u8* sub_y;
	u8* sub_uv;
	u32 i;
	//deg_Printf("taskWinHorProcess:%d\n",mainTaskOp.wintype);
	if(mainTaskOp.wintype == SUB_TO_MAIN_HOR_LEFT || mainTaskOp.wintype == MAIN_TO_SUB_HOR_RIGHT)
	{
		main_y  = mainTaskOp.sub_yaddr;
		main_uv = mainTaskOp.sub_uvaddr;
		sub_y	= mainTaskOp.main_yaddr;
		sub_uv  = mainTaskOp.main_uvaddr;
	}
	else if(mainTaskOp.wintype == MAIN_TO_SUB_HOR_LEFT || mainTaskOp.wintype == SUB_TO_MAIN_HOR_RIGHT)
	{
		sub_y  = mainTaskOp.sub_yaddr;
		sub_uv = mainTaskOp.sub_uvaddr;
		main_y	= mainTaskOp.main_yaddr;
		main_uv  = mainTaskOp.main_uvaddr;
	}else
	{
		return;
	}
	if(mainTaskOp.wintype == SUB_TO_MAIN_HOR_LEFT || mainTaskOp.wintype == MAIN_TO_SUB_HOR_LEFT)
	{
		sx = mainTaskOp.ratio_w - mainTaskOp.w_step;
	}else
	{
		sx = mainTaskOp.w_step;
	}

	ex = mainTaskOp.ratio_w;
	//deg_Printf("s [%d,%d]\n", sx, ex);
	u32 time = XOSTimeGet();
	while(1)
	{
		do {
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
			hal_wdtClear();
		}while(p_lcd_buffer == NULL);
		hal_lcdVideoFrameFlush(p_lcd_buffer, mainTaskOp.x, mainTaskOp.y, mainTaskOp.ratio_w, mainTaskOp.ratio_h, mainTaskOp.dest_w, mainTaskOp.dest_h);
		hx330x_sysDcacheInvalid((u32)p_lcd_buffer->y_addr,p_lcd_buffer->buf_size);
		for(i = 0; i < mainTaskOp.ratio_h; i++)
		{
			hal_wdtClear();

			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i, (void*)main_y + mainTaskOp.main_stride * i + ex - sx, sx);
			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i + sx, (void*)sub_y + mainTaskOp.main_stride * i,  ex - sx);
			if((i&1) == 0)
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2, (void*)main_uv + mainTaskOp.main_stride * i/2 + ex - sx, sx);
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2 + sx, (void*)sub_uv + mainTaskOp.main_stride * i/2,  ex - sx);
			}


		}
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		p_lcd_buffer = NULL;
		if(mainTaskOp.wintype == SUB_TO_MAIN_HOR_LEFT || mainTaskOp.wintype == MAIN_TO_SUB_HOR_LEFT)
		{
			if((ex - sx) >= mainTaskOp.ratio_w)
			{
				break;
			}

			sx -= mainTaskOp.w_step;
			if(sx < 0)
				sx = 0;
		}else
		{
			if(sx >= ex)
			{
				break;
			}
			sx += mainTaskOp.w_step;
			if(sx >= ex)
				sx = ex;
		}
		//deg_Printf("[%d,%d]\n", sx, ex);
		//deg_Printf("time:%dms\n", XOSTimeGet() - time );
		if((mainTaskOp.winChangeInterval + time) > XOSTimeGet() )
		{
			XOSTimeDly((mainTaskOp.winChangeInterval + time) - XOSTimeGet());
		}
		time = XOSTimeGet();
	}
	taskMainWinChangeUint();

}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskWinVorProcess(void)
{
	lcdshow_frame_t *p_lcd_buffer = NULL;
	s16 sy, ey;
	u8* main_y;
	u8* main_uv;
	u8* sub_y;
	u8* sub_uv;
	u32 i;
	//deg_Printf("taskWinVorProcess:%d\n",mainTaskOp.wintype);
	if(mainTaskOp.wintype == SUB_TO_MAIN_VOR_UP || mainTaskOp.wintype == MAIN_TO_SUB_VOR_DOWN)
	{
		main_y  = mainTaskOp.sub_yaddr;
		main_uv = mainTaskOp.sub_uvaddr;
		sub_y	= mainTaskOp.main_yaddr;
		sub_uv  = mainTaskOp.main_uvaddr;
	}
	else if(mainTaskOp.wintype == MAIN_TO_SUB_VOR_UP || mainTaskOp.wintype == SUB_TO_MAIN_VOR_DOWN)
	{
		sub_y  = mainTaskOp.sub_yaddr;
		sub_uv = mainTaskOp.sub_uvaddr;
		main_y	= mainTaskOp.main_yaddr;
		main_uv  = mainTaskOp.main_uvaddr;
	}else
	{
		return;
	}
	if(mainTaskOp.wintype == SUB_TO_MAIN_VOR_UP || mainTaskOp.wintype == MAIN_TO_SUB_VOR_UP)
	{
		sy = mainTaskOp.ratio_h - mainTaskOp.h_step;
	}else
	{
		sy = mainTaskOp.h_step;
	}

	ey = mainTaskOp.ratio_h;
	//deg_Printf("s [%d,%d]\n", sy, ey);
	u32 time = XOSTimeGet();
	while(1)
	{
		do {
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
			hal_wdtClear();
		}while(p_lcd_buffer == NULL);
		hal_lcdVideoFrameFlush(p_lcd_buffer, mainTaskOp.x, mainTaskOp.y, mainTaskOp.ratio_w, mainTaskOp.ratio_h, mainTaskOp.dest_w, mainTaskOp.dest_h);
		hx330x_sysDcacheInvalid((u32)p_lcd_buffer->y_addr,p_lcd_buffer->buf_size);
		for(i = 0; i < mainTaskOp.ratio_h; i++)
		{
			hal_wdtClear();
			if(i < sy)
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i, (void*)main_y + mainTaskOp.main_stride * (ey - sy + i), mainTaskOp.ratio_w);
				if((i&1) == 0)
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2, (void*)main_uv + mainTaskOp.main_stride *  (ey - sy + i)/2, mainTaskOp.ratio_w);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + mainTaskOp.main_stride * i, (void*)sub_y + mainTaskOp.main_stride * (i - sy), mainTaskOp.ratio_w);
				if((i&1) == 0)
					hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + mainTaskOp.main_stride * i/2, (void*)sub_uv + mainTaskOp.main_stride * (i - sy)/2, mainTaskOp.ratio_w);

			}
		}
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		p_lcd_buffer = NULL;
		if(mainTaskOp.wintype == SUB_TO_MAIN_VOR_UP || mainTaskOp.wintype == MAIN_TO_SUB_VOR_UP)
		{
			if((ey - sy) >= mainTaskOp.ratio_h)
			{
				break;
			}
			sy -= mainTaskOp.h_step;
			if(sy < 0)
				sy = 0;
		}else{
			if(sy >= ey)
			{
				break;
			}

			sy += mainTaskOp.h_step;
			if(sy > ey)
				sy = ey;
		}
		//deg_Printf("[%d,%d]\n", sy, ey);
		//deg_Printf("sy:%d\n", sy);
		//deg_Printf("time:%dms\n", XOSTimeGet() - time );
		if((mainTaskOp.winChangeInterval + time) > XOSTimeGet() )
		{
			XOSTimeDly((mainTaskOp.winChangeInterval + time) - XOSTimeGet());
		}
		time = XOSTimeGet();
	}
	taskMainWinChangeUint();

}
/*******************************************************************************
* Function Name  : taskWinChangeProcess
* Description    : taskWinChangeProcess function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
int taskWinChangeProcess(void)
{
	return;
	//if(SysCtrl.winChangeEnable == 0)
	{
		return -1;
	}
	// app_lcdPlayShowScaler_cfg(0,PLAY_SCALER_STAT_KICKBUF, 0);
	int res = taskMainWinChangeKick();
	taskWinCenterProcess();
	taskWinHorProcess();
	taskWinVorProcess();
	taskMainWinChangeUint();
	//return res;
	return -1;
}

/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskMainOpen(u32 arg)
{

	// if(SysCtrl.H63P_change_one==1)
	// {
	// 		//app_Cmos_Sensor_Switch();
	// 		//task_com_led_on();
	// 		app_lcdCsiVideoShowStop();
	// 		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_UINIT, hardware_setup.cmos_sensor_sel);
	// 		hardware_setup.cmos_sensor_sel ^=1;
	// 		XOSTimeDly(1000);
	// 		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);
	// 		//app_lcdCsiVideoShowStart();
	// 		app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	// 		SysCtrl.H63P_change_one=0;
	// }

	app_lcdCsiVideoShowStop();
	//task_com_sound_wait_end();
	res_music_end();

	if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST) //第一次开机，开机音
	{
		mainTaskOp.curId = 0;
		SysCtrl.dev_stat_power &= ~POWERON_FLAG_FIRST;
		taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_POWER_ON,0, SUB_TO_MAIN_VOR_DOWN);
//		SysCtrl.winChangeEnable = 1;
	}
	mainTaskOp.winShowToggleInterval = 500; //ms
	mainTaskOp.curIdShowBig = 1;
	if(taskWinChangeProcess() < 0)
	{
		mainTaskOp.curIdShowBig = 1;
		taskMainWinShowNoHandle();
	}else
	{
		mainTaskOp.winShowToggleCurtime = XOSTimeGet() - 300;
	}
		uiOpenWindow(&mainWindow,0,0);
	//app_draw_Service(1);
	task_com_auto_poweroff(1);
	task_com_spijpg_Init(1);
	task_com_sdlist_scan(1, 0);


	//hal_sysMemPrint();

}
/*******************************************************************************
* Function Name  : taskMainClose
* Description    : taskMainClose function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskMainClose(uint32 arg)
{
	//deg_Printf("taskMainClose\n");
	//if(mainTaskOp.uiDisable)
	//	hal_lcdUiEnable(UI_LAYER0,1);
	//task_com_sound_wait_end();
	res_music_end();
	task_com_auto_poweroff(1);
//	SysCtrl.winChangeEnable = 1;
	//hal_sysMemPrint();
}
/*******************************************************************************
* Function Name  : taskMainService
* Description    : taskMainService function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskMainService(uint32 arg)
{
	if(mainTaskOp.winShowToggleCurtime + mainTaskOp.winShowToggleInterval < XOSTimeGet())
	{
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}


}

ALIGNED(4) sysTask_T taskMain =
{
	"main",
	0,
	taskMainOpen,
	taskMainClose,
	taskMainService,
};


